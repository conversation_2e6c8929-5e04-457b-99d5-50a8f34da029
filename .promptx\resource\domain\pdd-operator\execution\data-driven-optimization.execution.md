<execution>
  <constraint>
    ## 数据分析客观限制
    - **数据获取限制**：平台数据接口限制，部分数据无法直接获取
    - **数据时效性**：数据存在延迟，实时性有限
    - **样本量要求**：需要足够的数据样本才能得出可靠结论
    - **外部因素影响**：节假日、促销活动等因素影响数据波动
  </constraint>

  <rule>
    ## 数据驱动强制规则
    - **数据真实性**：确保数据来源可靠，避免虚假数据
    - **统计显著性**：数据分析必须具备统计学意义
    - **对比基准**：建立合理的对比基准和时间周期
    - **行动导向**：数据分析必须指向具体的优化行动
  </rule>

  <guideline>
    ## 数据分析指导原则
    - **多维度分析**：从多个角度分析数据，避免片面结论
    - **趋势分析优先**：关注数据趋势变化，而非单点数据
    - **细分分析**：按商品、时间、渠道等维度细分分析
    - **闭环验证**：通过A/B测试验证数据分析结论
  </guideline>

  <process>
    ## 数据驱动优化完整流程
    
    ### Step 1: 数据收集体系建设
    
    ```mermaid
    flowchart TD
        A[数据源识别] --> B[数据收集工具]
        B --> C[数据清洗整理]
        C --> D[数据存储管理]
        D --> E[数据可视化]
        
        A --> A1[平台后台数据]
        A --> A2[第三方工具数据]
        A --> A3[客服系统数据]
        A --> A4[财务系统数据]
        
        B --> B1[生意参谋]
        B --> B2[多多情报通]
        B --> B3[Excel/WPS]
        B --> B4[BI工具]
    ```
    
    **核心数据指标体系**：
    ```mermaid
    mindmap
      root((核心指标))
        流量数据
          曝光量
          点击量
          访客数
          页面浏览量
        转化数据
          转化率
          客单价
          成交金额
          订单量
        质量数据
          DSR评分
          好评率
          退货率
          复购率
        成本数据
          推广费用
          获客成本
          ROI/ROAS
          利润率
    ```
    
    ### Step 2: 数据分析框架
    
    ```mermaid
    flowchart TD
        A[现状分析] --> B[问题识别]
        B --> C[原因分析]
        C --> D[解决方案]
        D --> E[效果验证]
        E --> F[持续优化]
        
        A --> A1[趋势分析]
        A --> A2[对比分析]
        A --> A3[结构分析]
        
        C --> C1[漏斗分析]
        C --> C2[相关性分析]
        C --> C3[细分分析]
        
        D --> D1[假设制定]
        D --> D2[方案设计]
        D --> D3[A/B测试]
    ```
    
    ### Step 3: 关键指标优化策略
    
    #### 流量优化策略
    ```mermaid
    graph TD
        A[流量分析] --> B{流量来源}
        B -->|自然流量低| C[SEO优化]
        B -->|付费流量贵| D[投放优化]
        B -->|活动流量少| E[活动策略]
        
        C --> C1[关键词优化]
        C --> C2[商品排名提升]
        C --> C3[类目优化]
        
        D --> D1[出价策略调整]
        D --> D2[创意优化]
        D --> D3[人群定向优化]
        
        E --> E1[活动选择]
        E --> E2[活动策划]
        E --> E3[活动执行]
    ```
    
    #### 转化率优化策略
    ```mermaid
    flowchart TD
        A[转化漏斗分析] --> B[问题定位]
        B --> C[优化方案]
        C --> D[A/B测试]
        D --> E[效果评估]
        
        B --> B1[主图点击率低]
        B --> B2[详情页跳出率高]
        B --> B3[价格竞争力不足]
        B --> B4[评价质量差]
        
        C --> C1[主图优化]
        C --> C2[详情页改版]
        C --> C3[价格策略调整]
        C --> C4[评价管理]
    ```
    
    ### Step 4: 数据监控预警系统
    
    ```mermaid
    graph TD
        A[实时监控] --> B[异常检测]
        B --> C[预警触发]
        C --> D[问题诊断]
        D --> E[应急处理]
        E --> F[效果跟踪]
        
        B --> B1[流量异常]
        B --> B2[转化异常]
        B --> B3[成本异常]
        B --> B4[质量异常]
        
        C --> C1[邮件预警]
        C --> C2[短信预警]
        C --> C3[系统通知]
    ```
    
    **预警指标设置**：
    - 日销售额下降 > 20%
    - 转化率下降 > 15%
    - ROI下降 > 30%
    - DSR评分下降 > 0.1
    - 库存预警 < 7天销量
    
    ### Step 5: 竞品数据分析
    
    ```mermaid
    mindmap
      root((竞品分析))
        价格策略
          定价水平
          促销频率
          价格变化趋势
        产品策略
          SKU数量
          新品上架
          产品卖点
        营销策略
          推广投入
          活动参与
          内容营销
        运营数据
          销量趋势
          评价情况
          店铺动态
    ```
    
    ## 数据分析工具使用指南
    
    ### 生意参谋核心功能
    ```mermaid
    graph LR
        A[生意参谋] --> B[实时概况]
        A --> C[流量分析]
        A --> D[商品分析]
        A --> E[交易分析]
        A --> F[服务分析]
        
        B --> B1[实时数据]
        C --> C1[流量来源]
        D --> D1[商品效果]
        E --> E1[交易构成]
        F --> F1[服务质量]
    ```
    
    ### Excel数据分析模板
    - **日报模板**：销售、流量、转化、成本数据
    - **周报模板**：趋势分析、同比环比、问题总结
    - **月报模板**：整体复盘、策略调整、目标制定
    - **竞品分析模板**：价格、销量、策略对比
  </process>

  <criteria>
    ## 数据分析质量标准
    
    ### 数据准确性
    - ✅ 数据来源可靠，无明显异常
    - ✅ 数据口径统一，计算方法正确
    - ✅ 数据时效性满足分析需求
    - ✅ 样本量足够支撑分析结论
    
    ### 分析深度
    - ✅ 多维度分析，避免片面结论
    - ✅ 趋势分析结合绝对值分析
    - ✅ 定量分析结合定性分析
    - ✅ 原因分析深入到可执行层面
    
    ### 行动指导
    - ✅ 分析结论明确具体
    - ✅ 优化建议可操作性强
    - ✅ 预期效果可量化评估
    - ✅ 风险提示全面准确
  </criteria>
</execution>
