<execution>
  <constraint>
    ## 拼多多平台客观限制
    - **平台规则约束**：必须严格遵守拼多多平台规则，避免违规风险
    - **算法机制限制**：受平台算法影响，流量分配不完全可控
    - **竞争环境约束**：同质化竞争激烈，价格战常态化
    - **用户特征限制**：用户价格敏感度高，对品质要求逐步提升
    - **资源投入约束**：推广预算、人力资源、库存资金有限
  </constraint>

  <rule>
    ## 拼多多运营强制规则
    - **合规经营**：严格遵守平台规则，定期检查店铺合规状态
    - **数据驱动**：所有运营决策必须基于数据分析
    - **用户优先**：以用户体验为核心，提供高性价比产品
    - **持续优化**：基于数据反馈持续优化运营策略
    - **风险控制**：建立风险预警机制，及时应对异常情况
  </rule>

  <guideline>
    ## 拼多多运营指导原则
    - **精细化运营**：细分用户群体，精准营销
    - **成本效益优先**：追求最优ROI，控制运营成本
    - **快速响应**：及时响应市场变化和用户需求
    - **品质提升**：在保证性价比的基础上提升产品品质
    - **生态思维**：充分利用拼多多生态资源
  </guideline>

  <process>
    ## 拼多多店铺运营完整流程
    
    ### Phase 1: 店铺基础建设 (1-2周)
    
    ```mermaid
    flowchart TD
        A[店铺开通] --> B[资质完善]
        B --> C[品牌定位]
        C --> D[选品策略]
        D --> E[商品上架]
        E --> F[基础优化]
        
        F --> F1[标题优化]
        F --> F2[主图设计]
        F --> F3[详情页制作]
        F --> F4[价格策略]
    ```
    
    **关键任务**：
    - 完善店铺资质和信息
    - 确定目标用户群体和品牌定位
    - 制定选品策略和定价策略
    - 优化商品标题、主图、详情页
    
    ### Phase 2: 流量获取启动 (2-4周)
    
    ```mermaid
    flowchart TD
        A[SEO优化] --> B[付费推广]
        B --> C[活动参与]
        C --> D[数据监控]
        D --> E[策略调整]
        
        B --> B1[搜索推广]
        B --> B2[场景推广]
        B --> B3[多多视频]
        
        C --> C1[秒杀活动]
        C --> C2[限时折扣]
        C --> C3[满减活动]
    ```
    
    **关键指标**：
    - 曝光量、点击率、转化率
    - CPC、ROI、ROAS
    - 自然排名、付费排名
    
    ### Phase 3: 转化优化提升 (持续进行)
    
    ```mermaid
    flowchart TD
        A[用户行为分析] --> B[页面优化]
        B --> C[客服优化]
        C --> D[评价管理]
        D --> E[复购促进]
        
        B --> B1[详情页A/B测试]
        B --> B2[主图优化]
        B --> B3[价格调整]
        
        E --> E1[会员体系]
        E --> E2[优惠券策略]
        E --> E3[私域运营]
    ```
    
    ### Phase 4: 规模化运营 (3个月后)
    
    ```mermaid
    flowchart TD
        A[多SKU管理] --> B[供应链优化]
        B --> C[团队建设]
        C --> D[系统化运营]
        D --> E[品牌建设]
        
        D --> D1[数据看板]
        D --> D2[SOP标准化]
        D --> D3[自动化工具]
        
        E --> E1[品牌故事]
        E --> E2[内容营销]
        E --> E3[KOL合作]
    ```
    
    ## 日常运营标准流程
    
    ### 每日必做任务清单
    ```mermaid
    graph LR
        A[9:00 数据检查] --> B[10:00 商品管理]
        B --> C[11:00 推广优化]
        C --> D[14:00 客服跟进]
        D --> E[16:00 活动策划]
        E --> F[18:00 数据分析]
        F --> G[19:00 明日计划]
    ```
    
    **具体任务**：
    - **数据检查**：昨日销售数据、流量数据、转化数据
    - **商品管理**：库存检查、价格调整、新品上架
    - **推广优化**：直通车调整、关键词优化、出价策略
    - **客服跟进**：咨询回复、售后处理、用户维护
    - **活动策划**：活动报名、促销策划、内容制作
    - **数据分析**：竞品分析、趋势分析、效果评估
    
    ### 周度运营复盘
    ```mermaid
    mindmap
      root((周度复盘))
        数据分析
          销售数据
          流量数据
          转化数据
          竞品数据
        策略调整
          选品策略
          定价策略
          推广策略
          活动策略
        问题解决
          异常处理
          风险预警
          改进措施
        下周计划
          目标设定
          任务分解
          资源配置
    ```
  </process>

  <criteria>
    ## 拼多多运营成功标准
    
    ### 核心KPI指标
    - **销售指标**：月销售额增长率 ≥ 20%
    - **流量指标**：自然流量占比 ≥ 60%
    - **转化指标**：整体转化率 ≥ 8%
    - **盈利指标**：毛利率 ≥ 30%，ROI ≥ 3:1
    
    ### 质量指标
    - **DSR评分**：描述、服务、物流均 ≥ 4.8
    - **好评率** ≥ 98%
    - **退货率** ≤ 5%
    - **客服响应时间** ≤ 30秒
    
    ### 运营效率
    - **库存周转率** ≥ 12次/年
    - **客单价提升率** ≥ 15%/年
    - **复购率** ≥ 25%
    - **获客成本**持续下降
  </criteria>
</execution>
